import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import Joi from 'joi'

// Joi validation schema for affiliate link update
const updateAffiliateLinkSchema = Joi.object({
  affiliateCode: Joi.string().min(3).max(50).pattern(/^[a-zA-Z0-9_-]+$/).messages({
    'string.min': 'Affiliate code must be at least 3 characters',
    'string.max': 'Affiliate code must not exceed 50 characters',
    'string.pattern.base': 'Affiliate code can only contain letters, numbers, underscores, and hyphens',
  }),
  targetLink: Joi.string().uri().messages({
    'string.uri': 'Target URL must be a valid URL',
  }),
  utmParams: Joi.object({
    source: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Source must not exceed 100 characters',
    }),
    medium: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Medium must not exceed 100 characters',
    }),
    campaign: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Campaign must not exceed 100 characters',
    }),
    term: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Term must not exceed 100 characters',
    }),
    content: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Content must not exceed 100 characters',
    }),
  }),
  event: Joi.number().optional().messages({
    'number.base': 'Event ID must be a number',
  }),
  promotionCode: Joi.string().optional().allow('').max(50).messages({
    'string.max': 'Promotion code must not exceed 50 characters',
  }),
  status: Joi.string().valid('active', 'disabled').messages({
    'any.only': 'Status must be either active or disabled',
  }),
})

// GET - Fetch single affiliate link
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const payload = await getPayload({ config: configPromise })

    // TODO: Get current user ID from session/auth
    const currentUserId = 1 // Replace with actual user ID from session

    const affiliateLink = await payload.findByID({
      collection: 'affiliate-links',
      id: parseInt(id),
      depth: 1,
    })

    if (!affiliateLink) {
      return NextResponse.json(
        {
          success: false,
          error: 'Affiliate link not found'
        },
        { status: 404 }
      )
    }

    // Check if the link belongs to the current user
    const ownerId = typeof affiliateLink.owner === 'object' ? affiliateLink.owner.id : affiliateLink.owner
    if (ownerId !== currentUserId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized access to this affiliate link'
        },
        { status: 403 }
      )
    }

    // Generate the full URL
    const params = new URLSearchParams()
    params.append('affiliate', affiliateLink.affiliateCode)
    
    if (affiliateLink.promotionCode) {
      params.append('promo_code', affiliateLink.promotionCode)
    }
    
    if (affiliateLink.utmParams) {
      const utmParams = affiliateLink.utmParams as any
      if (utmParams.source) params.append('utm_source', utmParams.source)
      if (utmParams.medium) params.append('utm_medium', utmParams.medium)
      if (utmParams.campaign) params.append('utm_campaign', utmParams.campaign)
      if (utmParams.term) params.append('utm_term', utmParams.term)
      if (utmParams.content) params.append('utm_content', utmParams.content)
    }

    const generatedUrl = affiliateLink.targetLink ? 
      `${affiliateLink.targetLink}?${params.toString()}` : 
      null

    return NextResponse.json({
      success: true,
      data: {
        id: affiliateLink.id,
        affiliateCode: affiliateLink.affiliateCode,
        promotionCode: affiliateLink.promotionCode,
        utmParams: affiliateLink.utmParams,
        targetLink: affiliateLink.targetLink,
        generatedUrl,
        status: affiliateLink.status,
        event: affiliateLink.event,
        createdAt: affiliateLink.createdAt,
        updatedAt: affiliateLink.updatedAt,
      },
    })

  } catch (error) {
    console.error('Error fetching affiliate link:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// PUT - Update affiliate link
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // Validate request body with Joi
    const { error, value } = updateAffiliateLinkSchema.validate(body, {
      abortEarly: false,
      stripUnknown: true
    })

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }))

      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    const payload = await getPayload({ config: configPromise })

    // TODO: Get current user ID from session/auth
    const currentUserId = 1 // Replace with actual user ID from session

    // First, check if the affiliate link exists and belongs to the user
    const existingLink = await payload.findByID({
      collection: 'affiliate-links',
      id: parseInt(id),
    })

    if (!existingLink) {
      return NextResponse.json(
        {
          success: false,
          error: 'Affiliate link not found'
        },
        { status: 404 }
      )
    }

    // Check ownership
    const ownerId = typeof existingLink.owner === 'object' ? existingLink.owner.id : existingLink.owner
    if (ownerId !== currentUserId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized access to this affiliate link'
        },
        { status: 403 }
      )
    }

    // Check if affiliate code is already in use by another link (if being updated)
    if (value.affiliateCode && value.affiliateCode !== existingLink.affiliateCode) {
      const duplicateCheck = await payload.find({
        collection: 'affiliate-links',
        where: {
          and: [
            {
              affiliateCode: {
                equals: value.affiliateCode,
              },
            },
            {
              id: {
                not_equals: parseInt(id),
              },
            },
          ],
        },
        limit: 1,
      })

      if (duplicateCheck.docs.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'This affiliate code is already in use. Please choose a different one.'
          },
          { status: 400 }
        )
      }
    }

    // Validate event exists if provided
    if (value.event) {
      const event = await payload.findByID({
        collection: 'events',
        id: value.event,
      }).catch(() => null)

      if (!event) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid event ID provided'
          },
          { status: 400 }
        )
      }
    }

    // Update the affiliate link
    const updatedLink = await payload.update({
      collection: 'affiliate-links',
      id: parseInt(id),
      data: {
        ...value,
        event: value.event || null,
        promotionCode: value.promotionCode || null,
      },
    })

    // Generate the full URL
    const params = new URLSearchParams()
    params.append('affiliate', updatedLink.affiliateCode)
    
    if (updatedLink.promotionCode) {
      params.append('promo_code', updatedLink.promotionCode)
    }
    
    if (updatedLink.utmParams) {
      const utmParams = updatedLink.utmParams as any
      if (utmParams.source) params.append('utm_source', utmParams.source)
      if (utmParams.medium) params.append('utm_medium', utmParams.medium)
      if (utmParams.campaign) params.append('utm_campaign', utmParams.campaign)
      if (utmParams.term) params.append('utm_term', utmParams.term)
      if (utmParams.content) params.append('utm_content', utmParams.content)
    }

    const generatedUrl = updatedLink.targetLink ? 
      `${updatedLink.targetLink}?${params.toString()}` : 
      null

    return NextResponse.json({
      success: true,
      data: {
        id: updatedLink.id,
        affiliateCode: updatedLink.affiliateCode,
        promotionCode: updatedLink.promotionCode,
        utmParams: updatedLink.utmParams,
        targetLink: updatedLink.targetLink,
        generatedUrl,
        status: updatedLink.status,
        event: updatedLink.event,
        createdAt: updatedLink.createdAt,
        updatedAt: updatedLink.updatedAt,
      },
    })

  } catch (error) {
    console.error('Error updating affiliate link:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// DELETE - Delete affiliate link
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const payload = await getPayload({ config: configPromise })

    // TODO: Get current user ID from session/auth
    const currentUserId = 1 // Replace with actual user ID from session

    // First, check if the affiliate link exists and belongs to the user
    const existingLink = await payload.findByID({
      collection: 'affiliate-links',
      id: parseInt(id),
    })

    if (!existingLink) {
      return NextResponse.json(
        {
          success: false,
          error: 'Affiliate link not found'
        },
        { status: 404 }
      )
    }

    // Check ownership
    const ownerId = typeof existingLink.owner === 'object' ? existingLink.owner.id : existingLink.owner
    if (ownerId !== currentUserId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unauthorized access to this affiliate link'
        },
        { status: 403 }
      )
    }

    // Delete the affiliate link
    await payload.delete({
      collection: 'affiliate-links',
      id: parseInt(id),
    })

    return NextResponse.json({
      success: true,
      message: 'Affiliate link deleted successfully',
    })

  } catch (error) {
    console.error('Error deleting affiliate link:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.'
      },
      { status: 500 }
    )
  }
}
