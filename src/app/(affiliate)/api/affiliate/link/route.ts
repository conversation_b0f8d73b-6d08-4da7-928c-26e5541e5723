import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import Joi from 'joi'

// Joi validation schema for affiliate link creation
const createAffiliateLinkSchema = Joi.object({
  affiliateCode: Joi.string().required().min(3).max(50).pattern(/^[a-zA-Z0-9_-]+$/).messages({
    'string.empty': 'Affiliate code is required',
    'string.min': 'Affiliate code must be at least 3 characters',
    'string.max': 'Affiliate code must not exceed 50 characters',
    'string.pattern.base': 'Affiliate code can only contain letters, numbers, underscores, and hyphens',
  }),
  targetLink: Joi.string().uri().required().messages({
    'string.empty': 'Target URL is required',
    'string.uri': 'Target URL must be a valid URL',
  }),
  utmParams: Joi.object({
    source: Joi.string().required().min(1).max(100).messages({
      'string.empty': 'UTM Source is required',
      'string.min': 'UTM Source must be at least 1 character',
      'string.max': 'UTM Source must not exceed 100 characters',
    }),
    medium: Joi.string().required().min(1).max(100).messages({
      'string.empty': 'UTM Medium is required',
      'string.min': 'UTM Medium must be at least 1 character',
      'string.max': 'UTM Medium must not exceed 100 characters',
    }),
    campaign: Joi.string().required().min(1).max(100).messages({
      'string.empty': 'UTM Campaign is required',
      'string.min': 'UTM Campaign must be at least 1 character',
      'string.max': 'UTM Campaign must not exceed 100 characters',
    }),
    term: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Term must not exceed 100 characters',
    }),
    content: Joi.string().optional().allow('').max(100).messages({
      'string.max': 'UTM Content must not exceed 100 characters',
    }),
  }).required(),
  event: Joi.number().optional().messages({
    'number.base': 'Event ID must be a number',
  }),
  promotionCode: Joi.string().optional().allow('').max(50).messages({
    'string.max': 'Promotion code must not exceed 50 characters',
  }),
})



// POST - Create new affiliate link
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate request body with Joi
    const { error, value } = createAffiliateLinkSchema.validate(body, {
      abortEarly: false,
      stripUnknown: true
    })

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }))

      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    const payload = await getPayload({ config: configPromise })

    // Check if affiliate code is already in use
    const existingAffiliate = await payload.find({
      collection: 'affiliate-links',
      where: {
        affiliateCode: {
          equals: value.affiliateCode,
        },
      },
      limit: 1,
    })

    if (existingAffiliate.docs.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'This affiliate code is already in use. Please choose a different one.'
        },
        { status: 400 }
      )
    }

    // TODO: Get current user ID from session/auth
    // For now, we'll use a placeholder - you'll need to implement proper auth
    const currentUserId = 36 // Replace with actual user ID from session

    // Validate event exists if provided
    if (value.event) {
      const event = await payload.findByID({
        collection: 'events',
        id: value.event,
      }).catch(() => null)

      if (!event) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid event ID provided'
          },
          { status: 400 }
        )
      }
    }

    console.log('value', value)

    // Create the affiliate link
    const affiliateLink = await payload.create({
      collection: 'affiliate-links',
      data: {
        owner: currentUserId,
        event: 4 || null,
        affiliateCode: value.affiliateCode,
        promotionCode: value.promotionCode || null,
        utmParams: value.utmParams,
        targetLink: value.targetLink,
        status: 'active',
      },
    })

    // Generate the full URL with UTM parameters for display
    const params = new URLSearchParams()
    if (affiliateLink.utmParams && typeof affiliateLink.utmParams === 'object') {
      const utmParams = affiliateLink.utmParams as any
      if (utmParams.source) params.append('utm_source', String(utmParams.source))
      if (utmParams.medium) params.append('utm_medium', String(utmParams.medium))
      if (utmParams.campaign) params.append('utm_campaign', String(utmParams.campaign))
      if (utmParams.term) params.append('utm_term', String(utmParams.term))
      if (utmParams.content) params.append('utm_content', String(utmParams.content))
    }
    params.append('affiliate', affiliateLink.affiliateCode)

    const generatedUrl = affiliateLink.targetLink ?
      `${affiliateLink.targetLink}?${params.toString()}` :
      null

    return NextResponse.json({
      success: true,
      data: {
        id: affiliateLink.id,
        affiliateCode: affiliateLink.affiliateCode,
        promotionCode: affiliateLink.promotionCode,
        utmParams: affiliateLink.utmParams,
        targetLink: affiliateLink.targetLink,
        generatedUrl,
        status: affiliateLink.status,
        createdAt: affiliateLink.createdAt,
      },
    })

  } catch (error) {
    console.error('Error creating affiliate link:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.'
      },
      { status: 500 }
    )
  }
}

// GET - Fetch affiliate links for current user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const status = searchParams.get('status')

    const payload = await getPayload({ config: configPromise })

    // TODO: Get current user ID from session/auth
    const currentUserId = 1 // Replace with actual user ID from session

    // Build where clause
    const where: any = {
      owner: {
        equals: currentUserId,
      },
    }

    if (status && ['active', 'disabled', 'expired'].includes(status)) {
      where.status = { equals: status }
    }

    const affiliateLinks = await payload.find({
      collection: 'affiliate-links',
      where,
      page,
      limit,
      sort: '-createdAt',
    })

    return NextResponse.json({
      success: true,
      data: affiliateLinks.docs,
      pagination: {
        page: affiliateLinks.page,
        limit: affiliateLinks.limit,
        totalPages: affiliateLinks.totalPages,
        totalDocs: affiliateLinks.totalDocs,
        hasNextPage: affiliateLinks.hasNextPage,
        hasPrevPage: affiliateLinks.hasPrevPage,
      },
    })

  } catch (error) {
    console.error('Error fetching affiliate links:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error. Please try again later.'
      },
      { status: 500 }
    )
  }
}